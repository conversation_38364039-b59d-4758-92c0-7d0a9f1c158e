import { Column, <PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { PurchaseOrder } from "./purchase-order.entity";
import { GoodsReceiptItem } from "../../goods-receipt/entities/goods-receipt-item.entity";

@Entity()
export class PurchaseOrderItem extends CustomBaseEntity {
    @ManyToOne(() => PurchaseOrder, (po) => po.items, { onDelete: 'CASCADE' })
    purchaseOrder: PurchaseOrder;

    @ManyToOne(() => Product, (product) => product.purchaseOrderItems)
    product: Product;

    @Column()
    productId: number;

    @Column()
    productName: string;

    @Column()
    productCode: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    receivedQuantity: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    remainingQuantity: number;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @OneToMany(() => GoodsReceiptItem, (grItem) => grItem.purchaseOrderItem)
    goodsReceiptItems: GoodsReceiptItem[];

    constructor(partial?: Partial<PurchaseOrderItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
