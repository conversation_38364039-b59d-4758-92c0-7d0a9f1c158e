import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { PurchaseOrderItem } from "./purchase-order-item.entity";
import { Supplier } from "../../supplier/entities/supplier.entity";
import { GoodsReceipt } from "../../goods-receipt/entities/goods-receipt.entity";

export enum PurchaseOrderStatus {
    DRAFT = "draft",           // ร่าง
    PENDING = "pending",       // รออนุมัติ
    APPROVED = "approved",     // อนุมัติแล้ว
    ORDERED = "ordered",       // สั่งซื้อแล้ว
    RECEIVED = "received",     // ได้รับสินค้าแล้ว
    COMPLETED = "completed",   // เสร็จสิ้น
    CANCELLED = "cancelled",   // ยกเลิก
}

@Entity()
export class PurchaseOrder extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    poNumber: string;

    @Column()
    poDate: Date;

    @Column({ nullable: true })
    expectedDeliveryDate: Date;

    @Column({ nullable: true })
    actualDeliveryDate: Date;

    @Column({ type: 'enum', enum: PurchaseOrderStatus, default: PurchaseOrderStatus.DRAFT })
    status: PurchaseOrderStatus;

    @ManyToOne(() => Supplier, (supplier) => supplier.purchaseOrders)
    supplier: Supplier;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    subtotal: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxRate: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discountAmount: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @ManyToOne(() => User, (user) => user.purchaseOrders)
    createdBy: User;

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User;

    @Column({ nullable: true })
    approvedAt: Date;

    @ManyToOne(() => Branch, (branch) => branch.purchaseOrders)
    branch: Branch;

    @OneToMany(() => PurchaseOrderItem, (item) => item.purchaseOrder, { cascade: true })
    items: PurchaseOrderItem[];

    @OneToMany(() => GoodsReceipt, (gr) => gr.purchaseOrder)
    goodsReceipts: GoodsReceipt[];

    constructor(partial?: Partial<PurchaseOrder>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
