import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Query,
  Patch
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { GoodsReceiptService, GOODS_RECEIPT_PAGINATION_CONFIG } from './goods-receipt.service';
import { CreateGoodsReceiptDto } from './dto/create-goods-receipt.dto';
import { UpdateGoodsReceiptDto } from './dto/update-goods-receipt.dto';
import { CreateGoodsReceiptFromPoDto } from './dto/create-goods-receipt-from-po.dto';
import { GoodsReceiptStatus } from './entities/goods-receipt.entity';

@Controller('goods-receipt')
@ApiTags('ใบรับสินค้า (Goods Receipt)')
@Auth()
export class GoodsReceiptController {
  constructor(private readonly goodsReceiptService: GoodsReceiptService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบรับสินค้าแบบ pagination' })
  @ApiPaginationQuery(GOODS_RECEIPT_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.goodsReceiptService.datatables(query);
  }

  @Get('generate-gr-number')
  @ApiOperation({ summary: 'สร้างเลขที่ใบรับสินค้าอัตโนมัติ' })
  @ApiResponse({ status: 200, description: 'สร้างเลขที่สำเร็จ' })
  generateGrNumber() {
    return this.goodsReceiptService.generateGrNumber();
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบรับสินค้าใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบรับสินค้าสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createGoodsReceiptDto: CreateGoodsReceiptDto) {
    const userId = req.user['sub'];
    return this.goodsReceiptService.create(createGoodsReceiptDto, userId);
  }

  @Post('from-purchase-order')
  @ApiOperation({ summary: 'สร้างใบรับสินค้าจาก Purchase Order' })
  @ApiResponse({ status: 201, description: 'สร้างใบรับสินค้าสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Purchase Order' })
  createFromPo(@Req() req: Request, @Body() createDto: CreateGoodsReceiptFromPoDto) {
    const userId = req.user['sub'];
    return this.goodsReceiptService.createFromPurchaseOrder(createDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบรับสินค้าทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.goodsReceiptService.findAll();
  }

  @Get('by-gr-number/:grNumber')
  @ApiOperation({ summary: 'ดึงข้อมูลใบรับสินค้าตามเลขที่ GR' })
  @ApiParam({ name: 'grNumber', description: 'เลขที่ใบรับสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบรับสินค้า' })
  findByGrNumber(@Param('grNumber') grNumber: string) {
    return this.goodsReceiptService.findByGrNumber(grNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบรับสินค้าตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบรับสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบรับสินค้า' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.goodsReceiptService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตใบรับสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบรับสินค้า' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบรับสินค้า' })
  update(
    @Param('id', ParseIntPipe) id: number, 
    @Body() updateGoodsReceiptDto: UpdateGoodsReceiptDto,
    @Req() req: Request
  ) {
    const userId = req.user['sub'];
    return this.goodsReceiptService.update(id, updateGoodsReceiptDto, userId);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบรับสินค้าและปรับปรุงสินค้าคงคลัง' })
  @ApiParam({ name: 'id', description: 'รหัสใบรับสินค้า' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบรับสินค้า' })
  approve(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.goodsReceiptService.approve(id, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบรับสินค้า' })
  @ApiParam({ name: 'id', description: 'รหัสใบรับสินค้า' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบรับสินค้า' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.goodsReceiptService.remove(id);
  }
}
