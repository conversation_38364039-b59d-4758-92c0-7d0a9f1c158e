import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { GoodsReceipt } from "./goods-receipt.entity";
import { PurchaseOrderItem } from "../../purchase-order/entities/purchase-order-item.entity";

@Entity()
export class GoodsReceiptItem extends CustomBaseEntity {
    @ManyToOne(() => GoodsReceipt, (gr) => gr.items, { onDelete: 'CASCADE' })
    goodsReceipt: GoodsReceipt;

    @Column()
    goodsReceiptId: number;

    @ManyToOne(() => Product, (product) => product.goodsReceiptItems)
    product: Product;

    @Column()
    productId: number;

    @Column()
    productName: string;

    @Column()
    productCode: string;

    @ManyToOne(() => PurchaseOrderItem, (poItem) => poItem.goodsReceiptItems, { nullable: true })
    purchaseOrderItem: PurchaseOrderItem;

    @Column({ nullable: true })
    purchaseOrderItemId: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    orderedQuantity: number; // จำนวนที่สั่ง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    receivedQuantity: number; // จำนวนที่รับจริง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย

    @Column({ type: 'text', nullable: true })
    batchNumber: string; // หมายเลข Batch

    @Column({ nullable: true })
    expiryDate: Date; // วันหมดอายุ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ default: false })
    isDefective: boolean; // สินค้าเสียหาย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    defectiveQuantity: number; // จำนวนที่เสียหาย

    constructor(partial?: Partial<GoodsReceiptItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
