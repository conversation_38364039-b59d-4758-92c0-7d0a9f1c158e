import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ReceiveItemFromPoDto {
    @ApiProperty({ description: 'รหัส PO Item' })
    @IsNumber()
    @IsNotEmpty()
    purchaseOrderItemId: number;

    @ApiProperty({ description: 'จำนวนที่รับจริง' })
    @IsNumber()
    @IsNotEmpty()
    receivedQuantity: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    unitCost?: number;

    @ApiProperty({ description: 'หมายเลข Batch', required: false })
    @IsOptional()
    @IsString()
    batchNumber?: string;

    @ApiProperty({ description: 'วันหมดอายุ', required: false })
    @IsOptional()
    @IsDateString()
    expiryDate?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'จำนวนที่เสียหาย', default: 0 })
    @IsOptional()
    @IsNumber()
    defectiveQuantity?: number;
}

export class CreateGoodsReceiptFromPoDto {
    @ApiProperty({ description: 'รหัส Purchase Order' })
    @IsNumber()
    @IsNotEmpty()
    purchaseOrderId: number;

    @ApiProperty({ description: 'เลขที่ใบรับสินค้า' })
    @IsString()
    @IsNotEmpty()
    grNumber: string;

    @ApiProperty({ description: 'วันที่รับสินค้า' })
    @IsDateString()
    grDate: string;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'เลขที่ใบส่งของ', required: false })
    @IsOptional()
    @IsString()
    deliveryNote?: string;

    @ApiProperty({ description: 'วันที่ส่งของ', required: false })
    @IsOptional()
    @IsDateString()
    deliveryDate?: string;

    @ApiProperty({ description: 'รหัสผู้รับสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    receivedById: number;

    @ApiProperty({ description: 'รายการสินค้าที่รับ', type: [ReceiveItemFromPoDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ReceiveItemFromPoDto)
    items: ReceiveItemFromPoDto[];
}
